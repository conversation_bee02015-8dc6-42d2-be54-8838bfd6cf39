document.addEventListener('DOMContentLoaded', () => {
    // Initialize Socket.IO connection
    const socket = io();

    // DOM elements
    const searchForm = document.getElementById('search-form');
    const productCodeInput = document.getElementById('product-code');
    const searchButton = document.getElementById('search-button');
    const loadingElement = document.getElementById('loading');
    const progressLog = document.getElementById('progress-log');
    const scrapersStatus = document.getElementById('scrapers-status');
    const resultsContainer = document.getElementById('results-container');
    const errorContainer = document.getElementById('error-container');
    const errorMessage = document.getElementById('error-message');
    const resultsTable = document.getElementById('results-table');
    const resultsBody = document.getElementById('results-body');
    const filterInput = document.getElementById('filter-input');
    const productCodeFilter = document.getElementById('product-code-filter');
    const supplierFilter = document.getElementById('supplier-filter');
    const brandFilter = document.getElementById('brand-filter');
    const availabilityAvailableFilter = document.getElementById('availability-available');
    const availabilityExternalFilter = document.getElementById('availability-external');
    const availabilityUnavailableFilter = document.getElementById('availability-unavailable');

    // Price range slider elements
    const priceSliderMin = document.getElementById('price-slider-min');
    const priceSliderMax = document.getElementById('price-slider-max');
    const priceMinValue = document.getElementById('price-min-value');
    const priceMaxValue = document.getElementById('price-max-value');
    const priceMinLabel = document.getElementById('price-min-label');
    const priceMaxLabel = document.getElementById('price-max-label');
    const priceRangeFill = document.getElementById('price-range-fill');


    // Error logs elements
    const errorLogsFab = document.getElementById('error-logs-fab');
    const errorCount = document.getElementById('error-count');
    const showErrorLogsBtn = document.getElementById('show-error-logs-btn');
    const errorLogsModal = document.getElementById('error-logs-modal');
    const closeErrorLogsModal = document.getElementById('close-error-logs-modal');
    const closeErrorLogsBtn = document.getElementById('close-error-logs-btn');
    const errorLogsContainer = document.getElementById('error-logs-container');

    // Stats elements
    const totalProducts = document.getElementById('total-products');
    const successfulScrapers = document.getElementById('successful-scrapers');
    const failedScrapers = document.getElementById('failed-scrapers');
    const uniqueBrands = document.getElementById('unique-brands');
    const availableProducts = document.getElementById('available-products');
    const unavailableProducts = document.getElementById('unavailable-products');
    const supplierStats = document.getElementById('supplier-stats');
    const welcomeHero = document.getElementById('welcome-hero');
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');
    const headerLogo = document.getElementById('header-logo');
    const footerLogo = document.getElementById('footer-logo');

    // Store the full results for filtering
    let allResults = [];

    // Store scraper errors
    let scraperErrors = {};

    // List of scrapers to display in the status section
    const scrapersList = [
        'AUTONET',
        'AUTOTOTAL',
        'INTERCARS',
        'AUTOPARTNER',
        'MATEROM',
        'ELIT',
        'BARDI'
    ];

    // Socket.IO event handlers
    socket.on('connect', () => {
        console.log('Connected to server');
    });

    socket.on('scraper-status', (data) => {
        console.log(`Received status update for ${data.supplier}: ${data.status}`, data);

        // Store logs if they exist
        if (data.logs && data.status === 'error') {
            // If we already have an error object for this scraper
            if (scraperErrors[data.supplier]) {
                scraperErrors[data.supplier].logs = data.logs;
            } else {
                scraperErrors[data.supplier] = {
                    message: data.message || 'Unknown error',
                    logs: data.logs
                };
            }

            // Update the FAB
            updateErrorLogsFab();
        }

        updateScraperStatus(data.supplier, data.status, data.message);
    });

    socket.on('log', (message) => {
        progressLog.textContent += message + '\n';
        progressLog.scrollTop = progressLog.scrollHeight; // Auto-scroll to bottom
    });

    socket.on('scraping-complete', (data) => {
        // Hide loading spinner
        loadingElement.classList.add('hidden');

        // Re-enable search button
        searchButton.disabled = false;
        searchButton.textContent = 'Search';

        // Process and display results
        processResults(data);
    });

    socket.on('scraping-error', (data) => {
        // Hide loading spinner
        loadingElement.classList.add('hidden');

        // Re-enable search button
        searchButton.disabled = false;
        searchButton.textContent = 'Search';

        // Show error message
        showError(data.error || 'An error occurred while fetching results');
    });

    // Initialize scraper status indicators
    function initializeScraperStatus() {
        console.log('Initializing scraper status indicators');
        // Clear any existing status indicators
        scrapersStatus.innerHTML = '';

        // Create a status indicator for each scraper
        scrapersList.forEach(scraper => {
            console.log(`Creating status indicator for ${scraper}`);

            // Create the main container using shadcn/ui card
            const statusItem = UI.createCard({
                className: 'animate-fade-in',
            });
            statusItem.id = `status-${scraper.toLowerCase()}`;

            // Create card content
            const cardContent = UI.createCardContent({
                className: 'px-5 py-4 pt-5'
            });

            // Create the header with scraper name
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-3';

            // Left side with scraper name
            const nameContainer = document.createElement('div');
            nameContainer.className = 'flex items-center space-x-2';

            const icon = document.createElement('i');
            icon.className = 'fas fa-robot text-primary';

            const nameSpan = document.createElement('span');
            nameSpan.className = 'font-medium';
            nameSpan.textContent = scraper;

            nameContainer.appendChild(icon);
            nameContainer.appendChild(nameSpan);

            // Add name container to header
            header.appendChild(nameContainer);

            // Create the status container
            const statusContainer = document.createElement('div');
            statusContainer.className = 'flex items-center justify-between';

            // Create status message container (left)
            const statusMessage = document.createElement('div');
            statusMessage.className = 'text-sm text-muted-foreground';
            statusMessage.id = `status-message-${scraper.toLowerCase()}`;
            statusMessage.textContent = 'Starting...';

            // Create status icon container (right)
            const statusIcon = document.createElement('span');
            statusIcon.className = 'flex items-center';

            const spinnerIcon = document.createElement('i');
            spinnerIcon.className = 'fas fa-spinner fa-spin text-primary';
            statusIcon.appendChild(spinnerIcon);

            // Add message and icon to status container
            statusContainer.appendChild(statusMessage);
            statusContainer.appendChild(statusIcon);

            // Add both containers to card content
            cardContent.appendChild(header);
            cardContent.appendChild(statusContainer);

            // Add card content to status item
            statusItem.appendChild(cardContent);

            // Add the status item to the page
            scrapersStatus.appendChild(statusItem);
        });
    }

    // Update scraper status
    function updateScraperStatus(scraper, status, message = '') {
        console.log(`Updating status for ${scraper} to ${status}`);

        // Find the status item by ID
        let statusItem = document.getElementById(`status-${scraper.toLowerCase()}`);

        // If the status item doesn't exist, create it
        if (!statusItem) {
            console.log(`Creating new status item for ${scraper}`);

            // Create the main container
            statusItem = document.createElement('div');
            statusItem.className = 'flex flex-col p-4 bg-card rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow mb-2';
            statusItem.id = `status-${scraper.toLowerCase()}`;

            // Create the header with scraper name
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-2';

            // Left side with scraper name
            const nameContainer = document.createElement('div');
            nameContainer.className = 'flex items-center';

            const robotIcon = document.createElement('i');
            robotIcon.className = 'fas fa-robot text-primary mr-3';

            const nameSpan = document.createElement('span');
            nameSpan.className = 'font-medium text-foreground';
            nameSpan.textContent = scraper;

            nameContainer.appendChild(robotIcon);
            nameContainer.appendChild(nameSpan);

            // Add name container to header
            header.appendChild(nameContainer);

            // Create the status container
            const statusContainer = document.createElement('div');
            statusContainer.className = 'flex items-center justify-between mt-1';

            // Create status message container (left)
            const statusMessage = document.createElement('div');
            statusMessage.className = 'text-sm text-muted-foreground';
            statusMessage.id = `status-message-${scraper.toLowerCase()}`;

            // Create status icon container (right)
            const statusIcon = document.createElement('span');
            statusIcon.className = 'flex items-center';

            // Add message and icon to status container
            statusContainer.appendChild(statusMessage);
            statusContainer.appendChild(statusIcon);

            // Add both containers to the main container
            statusItem.appendChild(header);
            statusItem.appendChild(statusContainer);

            // Add the status item to the page
            scrapersStatus.appendChild(statusItem);
        }

        // Get the status container elements
        const statusContainer = statusItem.querySelector('div:nth-child(2)');
        const statusIcon = statusContainer.querySelector('span');
        const statusMessage = statusContainer.querySelector(`#status-message-${scraper.toLowerCase()}`);

        if (!statusIcon || !statusMessage) {
            console.error(`Status elements for ${scraper} not found`);
            return;
        }

        // Clear any previous content
        statusIcon.innerHTML = '';

        // Update status message
        if (message) {
            statusMessage.textContent = message;
        } else {
            statusMessage.textContent = '';
        }

        // Create the appropriate icon based on status
        const icon = document.createElement('i');

        switch (status) {
            case 'loading':
                icon.className = 'fas fa-spinner fa-spin text-primary';
                statusIcon.appendChild(icon);
                statusMessage.className = 'text-sm text-muted-foreground';
                break;

            case 'success':
                icon.className = 'fas fa-check-circle text-green-500 text-xl';
                statusIcon.appendChild(icon);
                statusMessage.className = 'text-sm text-green-600 font-medium';
                break;

            case 'error':
                icon.className = 'fas fa-times-circle text-red-500 text-xl';
                statusIcon.appendChild(icon);
                statusMessage.className = 'text-sm text-red-600 font-medium';

                // Track the error for this scraper
                // If we already have logs for this scraper, preserve them
                if (typeof scraperErrors[scraper] === 'object' && scraperErrors[scraper].logs) {
                    scraperErrors[scraper] = {
                        message: message,
                        logs: scraperErrors[scraper].logs
                    };
                } else {
                    // Otherwise just store the message
                    scraperErrors[scraper] = {
                        message: message,
                        logs: ''
                    };
                }

                // Update error count and show FAB
                updateErrorLogsFab();
                break;
        }
    }

    // Handle form submission
    searchForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const productCode = productCodeInput.value.trim();
        if (!productCode) {
            showError('Please enter a product code');
            return;
        }

        // Reset UI
        resetUI();

        // Show loading spinner
        loadingElement.classList.remove('hidden');
        progressLog.textContent = `Starting search for product code: ${productCode}...\n`;

        // Initialize scraper status indicators
        initializeScraperStatus();

        // Disable search button
        searchButton.disabled = true;
        searchButton.textContent = 'Searching...';

        try {
            // Make API request with socket ID
            const response = await fetch('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    productCode,
                    socketId: socket.id
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to fetch results');
            }

            // The actual results will come through the socket.io connection

        } catch (error) {
            // Show error and re-enable search button
            showError(error.message || 'An error occurred while fetching results');
            searchButton.disabled = false;
            searchButton.textContent = 'Search';

            // Mark all scrapers as failed
            scrapersList.forEach(scraper => {
                updateScraperStatus(scraper, 'error');
            });
        }
    });

    // Process and display results
    function processResults(data) {
        if (!data.success) {
            showError(data.error || 'Failed to fetch results');
            return;
        }

        // Store results for filtering
        allResults = extractAllProducts(data.results);

        // Check for errors in the results
        Object.entries(data.results).forEach(([supplier, result]) => {
            if (!result.success || result.data === null) {
                // Add to scraper errors
                const errorMessage = result.error || `${supplier} scraper failed to retrieve data`;

                // Store both the error message and logs
                scraperErrors[supplier] = {
                    message: errorMessage,
                    logs: result.logs || ''
                };

                // Also update the status display to show error
                updateScraperStatus(supplier, 'error', errorMessage);
            }
        });

        // Update error logs FAB if there are errors
        updateErrorLogsFab();

        // Update statistics
        updateStatistics(data.stats);

        // Populate filter dropdowns
        populateFilters(allResults);

        // Display results table
        displayResults(allResults);

        // Show results container
        resultsContainer.classList.remove('hidden');
    }

    // Calculate similarity between two strings using Levenshtein distance
    function calculateSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;

        str1 = str1.toLowerCase().trim();
        str2 = str2.toLowerCase().trim();

        if (str1 === str2) return 1;

        const len1 = str1.length;
        const len2 = str2.length;

        // Create a matrix for dynamic programming
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

        // Initialize first row and column
        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;

        // Fill the matrix
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // deletion
                    matrix[i][j - 1] + 1,      // insertion
                    matrix[i - 1][j - 1] + cost // substitution
                );
            }
        }

        const maxLen = Math.max(len1, len2);
        return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
    }

    // Find the best matching product code from other suppliers for a BARDI code
    function findBestMatch(bardiCode, otherSupplierCodes) {
        if (!bardiCode || otherSupplierCodes.length === 0) return null;

        for (const otherCode of otherSupplierCodes) {
            if (!otherCode) continue;

            // 1. Direct exact match (case insensitive)
            if (bardiCode.toLowerCase() === otherCode.toLowerCase()) {
                return { code: otherCode, similarity: 1.0, matchType: 'exact' };
            }

            // 2. Space normalization (case: "0986424797" vs "0 986 424 797")
            const bardiNoSpaces = bardiCode.replace(/\s+/g, '');
            const otherNoSpaces = otherCode.replace(/\s+/g, '');
            if (bardiNoSpaces === otherNoSpaces) {
                return { code: otherCode, similarity: 1.0, matchType: 'space_normalized' };
            }

            // 3. Prefix removal - BARDI code might have a prefix added
            // Only try this if BARDI code is longer and contains the other code as exact suffix
            if (bardiCode.length > otherCode.length) {
                const suffix = bardiCode.slice(-otherCode.length);
                if (suffix.toLowerCase() === otherCode.toLowerCase()) {
                    // Additional validation: check if the prefix is reasonable length
                    const prefix = bardiCode.slice(0, -otherCode.length);
                    // Only accept if prefix is reasonable length (2-10 chars) to avoid false matches
                    if (prefix.length >= 2 && prefix.length <= 10) {
                        return { code: otherCode, similarity: 1.0, matchType: 'prefix_removed' };
                    }
                }
            }

            // 4. Space + prefix combination (e.g., "TRW GDB1550" vs "GDB1550")
            if (bardiCode.length > otherCode.length) {
                const bardiNoSpacesForPrefix = bardiCode.replace(/\s+/g, '');
                if (bardiNoSpacesForPrefix.length > otherCode.length) {
                    const suffix = bardiNoSpacesForPrefix.slice(-otherCode.length);
                    if (suffix.toLowerCase() === otherCode.toLowerCase()) {
                        const prefix = bardiNoSpacesForPrefix.slice(0, -otherCode.length);
                        // Only accept if prefix is reasonable length (2-10 chars)
                        if (prefix.length >= 2 && prefix.length <= 10) {
                            return { code: otherCode, similarity: 1.0, matchType: 'space_and_prefix_removed' };
                        }
                    }
                }
            }
        }

        // NO FUZZY MATCHING - we only accept exact transformations
        // This prevents incorrect matches like "13.0460-7117.2" with "13.0460-7184.2"
        return null;
    }

    // Normalize BARDI product codes by matching them against other supplier codes
    function normalizeBARDICodes(allProducts) {
        // Separate BARDI products from others
        const bardiProducts = allProducts.filter(p => p.supplier === 'BARDI');
        const otherProducts = allProducts.filter(p => p.supplier !== 'BARDI');

        // Get all unique product codes from other suppliers
        const otherSupplierCodes = [...new Set(otherProducts.map(p => p.productCode || p.code).filter(Boolean))];

        // Process each BARDI product
        bardiProducts.forEach(bardiProduct => {
            const bardiCode = bardiProduct.productCode || bardiProduct.code || '';
            if (!bardiCode) return;

            // Try to find a match with other supplier codes
            const match = findBestMatch(bardiCode, otherSupplierCodes);

            if (match) {
                // Store original BARDI code and use matched code as normalized
                bardiProduct.originalBardiCode = bardiCode;
                bardiProduct.productCode = match.code;
                bardiProduct.matchInfo = {
                    similarity: match.similarity,
                    matchType: match.matchType
                };
            }
            // If no match found, keep original BARDI code
        });

        return allProducts;
    }

    // Extract all products from results
    function extractAllProducts(results) {
        const products = [];

        Object.keys(results).forEach(supplier => {
            const supplierResult = results[supplier];

            if (supplierResult.success && supplierResult.data && supplierResult.data.length > 0) {
                supplierResult.data.forEach(product => {
                    // Add supplier to product
                    products.push({
                        ...product,
                        supplier: supplier
                    });
                });
            }
        });

        // Apply BARDI normalization by comparing against other supplier codes
        const normalizedProducts = normalizeBARDICodes(products);

        return normalizedProducts;
    }

    // Update statistics display
    function updateStatistics(stats) {
        totalProducts.textContent = stats.totalProducts;
        successfulScrapers.textContent = stats.totalSuccessfulScrapers;
        failedScrapers.textContent = stats.totalFailedScrapers;
        uniqueBrands.textContent = stats.uniqueBrands.length;
        availableProducts.textContent = stats.availability.available;
        unavailableProducts.textContent = stats.availability.unavailable;

        // Display supplier stats
        supplierStats.innerHTML = '';
        Object.entries(stats.productsPerSupplier).forEach(([supplier, count]) => {
            const badge = UI.createBadge({
                variant: 'secondary',
                className: 'flex items-center space-x-2 animate-slide-in',
                children: `<i class="fas fa-building"></i><span>${supplier}: ${count}</span>`
            });
            supplierStats.appendChild(badge);
        });
    }

    // Initialize price range slider based on product data
    function initializePriceSlider(products) {
        // Group products by product code and find price ranges
        const groupedProducts = groupProductsByCode(products);
        const cheapestPrices = [];

        Object.values(groupedProducts).forEach(productGroup => {
            const cheapestPrice = getCheapestPrice(productGroup);
            if (cheapestPrice > 0) {
                cheapestPrices.push(cheapestPrice);
            }
        });

        if (cheapestPrices.length === 0) {
            // No valid prices found, use default range
            const defaultMin = 0;
            const defaultMax = 1000;

            priceSliderMin.min = defaultMin;
            priceSliderMin.max = defaultMax;
            priceSliderMin.value = defaultMin;

            priceSliderMax.min = defaultMin;
            priceSliderMax.max = defaultMax;
            priceSliderMax.value = defaultMax;

            priceMinLabel.textContent = `${defaultMin} RON`;
            priceMaxLabel.textContent = `${defaultMax} RON`;
            priceMinValue.textContent = `${defaultMin} RON`;
            priceMaxValue.textContent = `${defaultMax} RON`;

            updateRangeFill();
            return;
        }

        const minPrice = Math.min(...cheapestPrices);
        const maxPrice = Math.max(...cheapestPrices);

        // Add some padding to the range
        const paddedMin = Math.max(0, Math.floor(minPrice * 0.9));
        const paddedMax = Math.ceil(maxPrice * 1.1);

        // Update both sliders
        priceSliderMin.min = paddedMin;
        priceSliderMin.max = paddedMax;
        priceSliderMin.value = paddedMin; // Start with min value

        priceSliderMax.min = paddedMin;
        priceSliderMax.max = paddedMax;
        priceSliderMax.value = paddedMax; // Start with max value (show all)

        // Update labels
        priceMinLabel.textContent = `${paddedMin} RON`;
        priceMaxLabel.textContent = `${paddedMax} RON`;
        priceMinValue.textContent = `${paddedMin} RON`;
        priceMaxValue.textContent = `${paddedMax} RON`;

        updateRangeFill();
    }

    // Update the visual fill between the two slider handles
    function updateRangeFill() {
        const min = parseInt(priceSliderMin.value);
        const max = parseInt(priceSliderMax.value);
        const sliderMin = parseInt(priceSliderMin.min);
        const sliderMax = parseInt(priceSliderMax.max);

        const leftPercent = ((min - sliderMin) / (sliderMax - sliderMin)) * 100;
        const rightPercent = ((max - sliderMin) / (sliderMax - sliderMin)) * 100;

        priceRangeFill.style.left = leftPercent + '%';
        priceRangeFill.style.width = (rightPercent - leftPercent) + '%';
    }

    // Populate filter dropdowns
    function populateFilters(products) {
        // Initialize price slider
        initializePriceSlider(products);

        // Clear existing options
        productCodeFilter.innerHTML = '<option value="all">All Product Codes</option>';
        supplierFilter.innerHTML = '<option value="all">All Suppliers</option>';
        brandFilter.innerHTML = '<option value="all">All Brands</option>';

        // Get unique product codes, suppliers and brands
        const productCodes = new Set();
        const suppliers = new Set();
        const brands = new Set();

        products.forEach(product => {
            const code = product.productCode || product.code;
            if (code) productCodes.add(code);
            // Also add original Bardi code to the filter options if it exists and is different
            if (product.originalBardiCode && product.originalBardiCode !== code) {
                productCodes.add(product.originalBardiCode);
            }
            if (product.supplier) suppliers.add(product.supplier);
            if (product.brand) brands.add(product.brand);
        });

        // Add product code options
        Array.from(productCodes).sort().forEach(code => {
            const option = document.createElement('option');
            option.value = code;
            option.textContent = code;
            productCodeFilter.appendChild(option);
        });

        // Add supplier options
        Array.from(suppliers).sort().forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier;
            option.textContent = supplier;
            supplierFilter.appendChild(option);
        });

        // Add brand options
        Array.from(brands).sort().forEach(brand => {
            const option = document.createElement('option');
            option.value = brand;
            option.textContent = brand;
            brandFilter.appendChild(option);
        });

        // Add event listeners for filters
        filterInput.addEventListener('input', applyFilters);
        productCodeFilter.addEventListener('change', applyFilters);
        supplierFilter.addEventListener('change', applyFilters);
        brandFilter.addEventListener('change', applyFilters);
        availabilityAvailableFilter.addEventListener('change', applyFilters);
        availabilityExternalFilter.addEventListener('change', applyFilters);
        availabilityUnavailableFilter.addEventListener('change', applyFilters);

        // Add price range slider event listeners
        priceSliderMin.addEventListener('input', function() {
            let minVal = parseInt(this.value);
            let maxVal = parseInt(priceSliderMax.value);

            // Ensure min doesn't exceed max
            if (minVal > maxVal) {
                minVal = maxVal;
                this.value = minVal;
            }

            priceMinValue.textContent = `${minVal} RON`;
            updateRangeFill();
            applyFilters();
        });

        priceSliderMax.addEventListener('input', function() {
            let maxVal = parseInt(this.value);
            let minVal = parseInt(priceSliderMin.value);

            // Ensure max doesn't go below min
            if (maxVal < minVal) {
                maxVal = minVal;
                this.value = maxVal;
            }

            priceMaxValue.textContent = `${maxVal} RON`;
            updateRangeFill();
            applyFilters();
        });

    }

    // Apply filters to results
    function applyFilters() {
        const filterText = filterInput.value.toLowerCase();
        const productCodeValue = productCodeFilter.value;
        const supplierValue = supplierFilter.value;
        const brandValue = brandFilter.value;
        const minPrice = parseInt(priceSliderMin.value);
        const maxPrice = parseInt(priceSliderMax.value);

        // Get availability filter values
        const availabilityFilters = [];
        if (availabilityAvailableFilter.checked) {
            availabilityFilters.push('Available');
        }
        if (availabilityExternalFilter.checked) {
            availabilityFilters.push('External Delivery Only');
        }
        if (availabilityUnavailableFilter.checked) {
            availabilityFilters.push('Unavailable');
        }

        // First, filter individual products
        const filteredProducts = allResults.filter(product => {
            // Text filter
            const matchesText =
                (product.name && product.name.toLowerCase().includes(filterText)) ||
                (product.brand && product.brand.toLowerCase().includes(filterText)) ||
                (product.productCode && product.productCode.toLowerCase().includes(filterText)) ||
                (product.code && product.code.toLowerCase().includes(filterText)) ||
                (product.originalBardiCode && product.originalBardiCode.toLowerCase().includes(filterText));

            // Product code filter
            const productCode = product.productCode || product.code || '';
            // Also match on original Bardi code if available
            const matchesProductCode = productCodeValue === 'all' ||
                                      productCode === productCodeValue ||
                                      (product.originalBardiCode && product.originalBardiCode === productCodeValue);

            // Supplier filter
            const matchesSupplier = supplierValue === 'all' || product.supplier === supplierValue;

            // Brand filter
            const matchesBrand = brandValue === 'all' || product.brand === brandValue;

            // Availability filter - if no checkboxes are selected, show no results
            const matchesAvailability = availabilityFilters.length > 0 && (
                                       availabilityFilters.includes(product.availabilitySummary) ||
                                       // If no availabilitySummary, treat as unavailable for filtering purposes
                                       (!product.availabilitySummary && availabilityFilters.includes('Unavailable'))
                                       );

            return matchesText && matchesProductCode && matchesSupplier && matchesBrand && matchesAvailability;
        });

        // Group filtered products and apply price filter at group level
        const groupedProducts = groupProductsByCode(filteredProducts);
        const priceFilteredProducts = [];

        Object.entries(groupedProducts).forEach(([productCode, products]) => {
            const cheapestPrice = getCheapestPrice(products);

            // Include group if cheapest price is within the slider range
            if (cheapestPrice >= minPrice && cheapestPrice <= maxPrice) {
                priceFilteredProducts.push(...products);
            }
        });

        displayResults(priceFilteredProducts);
    }

    // Display results in table
    function displayResults(products) {
        resultsBody.innerHTML = '';

        if (products.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 7; // Fixed 7 columns: Product Code, Brand, Supplier, Price, Status, Availability, Delivery
            cell.className = 'no-results-cell py-8 text-center text-muted-foreground';
            cell.textContent = 'No results found';
            row.appendChild(cell);
            resultsBody.appendChild(row);
            return;
        }

        // Group products by product code
        const groupedProducts = groupProductsByCode(products);

        // Render grouped products
        renderGroupedProducts(groupedProducts);
    }

    // Group products by their product code
    function groupProductsByCode(products) {
        const grouped = {};

        products.forEach(product => {
            // Use the normalized product code for grouping, not the original Bardi code
            const productCode = product.productCode || product.code || 'Unknown';

            if (!grouped[productCode]) {
                grouped[productCode] = [];
            }

            grouped[productCode].push(product);
        });

        return grouped;
    }

    // Get the cheapest price from a group of products
    function getCheapestPrice(products) {
        let cheapestPrice = Infinity;

        products.forEach(product => {
            let price = product.priceGross || product.retailPrice || product.price || '0';

            // Convert string prices to numbers
            if (typeof price === 'string') {
                price = parseFloat(price.replace(/[^\d.,]/g, '').replace(',', '.')) || 0;
            }

            if (price > 0 && price < cheapestPrice) {
                cheapestPrice = price;
            }
        });

        // Return 0 if no valid price found (will be sorted to the top)
        return cheapestPrice === Infinity ? 0 : cheapestPrice;
    }

    // Render grouped products in the table
    function renderGroupedProducts(groupedProducts) {
        // Get product codes and sort them by the cheapest price within each group
        const productCodes = Object.keys(groupedProducts).sort((codeA, codeB) => {
            const productsA = groupedProducts[codeA];
            const productsB = groupedProducts[codeB];

            // Find the cheapest price in each group
            const cheapestPriceA = getCheapestPrice(productsA);
            const cheapestPriceB = getCheapestPrice(productsB);

            // Sort by cheapest price (ascending)
            return cheapestPriceA - cheapestPriceB;
        });

        // For each product code
        productCodes.forEach(productCode => {
            const products = groupedProducts[productCode];
            let isFirstRow = true;

            // Sort products by price only (ascending)
            const sortedProducts = [...products].sort((a, b) => {
                // Extract price values - handle string values with currency symbols
                let priceA = a.priceGross || a.retailPrice || a.price || '0';
                let priceB = b.priceGross || b.retailPrice || b.price || '0';

                // Convert string prices to numbers
                if (typeof priceA === 'string') {
                    priceA = parseFloat(priceA.replace(/[^\d.,]/g, '').replace(',', '.')) || 0;
                }
                if (typeof priceB === 'string') {
                    priceB = parseFloat(priceB.replace(/[^\d.,]/g, '').replace(',', '.')) || 0;
                }

                // Sort ascending by price only
                return priceA - priceB;
            });

            // For each product with this code
            sortedProducts.forEach((product, index) => {
                const row = UI.createTableRow({
                    className: `animate-fade-in product-row-${productCode.replace(/[^a-zA-Z0-9]/g, '_')}`,
                    'data-product-code': productCode,
                    'data-row-index': index
                });

                // Hide all rows except the first one (cheapest) by default
                if (index > 0) {
                    row.style.display = 'none';
                    row.classList.add('collapsed-row');
                }

                // Find the most frequent brand for this product code group
                const brandCounts = {};
                let mostFrequentBrand = '';
                let maxCount = 0;

                products.forEach(p => {
                    if (p.brand) {
                        brandCounts[p.brand] = (brandCounts[p.brand] || 0) + 1;
                        if (brandCounts[p.brand] > maxCount) {
                            maxCount = brandCounts[p.brand];
                            mostFrequentBrand = p.brand;
                        }
                    }
                });

                // Product Code (shared column - only shown in the first row of each group)
                const codeCell = UI.createTableCell({
                    className: isFirstRow ? 'font-bold text-primary bg-muted/50 border-r' : ''
                });
                if (isFirstRow) {
                    codeCell.rowSpan = 1; // Start with 1, will be updated by toggle function
                    codeCell.setAttribute('data-original-rowspan', products.length); // Store original rowspan

                    // Create a container for the product code, name, and supplier count
                    const codeContainer = document.createElement('div');
                    codeContainer.className = 'flex flex-col';

                    // Add the product code
                    const codeText = document.createElement('div');
                    codeText.className = 'text-lg font-semibold';
                    codeText.textContent = productCode;
                    codeContainer.appendChild(codeText);

                    // Add the product name as a subtitle (get the most common name from the group)
                    const productNames = products.map(p => p.name).filter(name => name && name.trim());
                    if (productNames.length > 0) {
                        // Find the most frequent name or use the first one
                        const nameCounts = {};
                        let mostFrequentName = productNames[0];
                        let maxCount = 0;

                        productNames.forEach(name => {
                            nameCounts[name] = (nameCounts[name] || 0) + 1;
                            if (nameCounts[name] > maxCount) {
                                maxCount = nameCounts[name];
                                mostFrequentName = name;
                            }
                        });

                        const nameText = document.createElement('div');
                        nameText.className = 'text-sm text-muted-foreground mt-1 font-normal';
                        nameText.textContent = mostFrequentName;
                        codeContainer.appendChild(nameText);
                    }

                    // If this is a Bardi product with an original code that's different from the normalized one,
                    // show the original code as well
                    const bardiProduct = products.find(p => p.originalBardiCode && p.originalBardiCode !== productCode);
                    if (bardiProduct) {
                        const originalCodeText = document.createElement('div');
                        originalCodeText.className = 'text-xs text-muted-foreground mt-1';
                        originalCodeText.innerHTML = `<span class="font-medium">Bardi code:</span> ${bardiProduct.originalBardiCode}`;
                        codeContainer.appendChild(originalCodeText);
                    }

                    // Add the supplier count with collapse/expand functionality
                    const supplierCountContainer = document.createElement('div');
                    supplierCountContainer.className = 'flex items-center justify-between mt-2';

                    const supplierCount = document.createElement('div');
                    supplierCount.className = 'text-xs text-muted-foreground';
                    supplierCount.textContent = `${products.length} supplier${products.length !== 1 ? 's' : ''}`;
                    supplierCountContainer.appendChild(supplierCount);

                    // Add collapse/expand button if there are multiple suppliers
                    if (products.length > 1) {
                        const toggleButton = document.createElement('button');
                        toggleButton.className = 'ml-2 px-2 py-1 text-xs bg-primary/10 hover:bg-primary/20 text-primary rounded transition-colors flex items-center space-x-1 collapse-toggle-btn';
                        toggleButton.setAttribute('data-product-code', productCode);
                        toggleButton.innerHTML = `
                            <span class="toggle-text">Show All</span>
                            <i class="fas fa-chevron-down toggle-icon transition-transform"></i>
                        `;

                        // Add click event listener for toggle functionality
                        toggleButton.addEventListener('click', function() {
                            toggleProductGroup(productCode, this);
                        });

                        supplierCountContainer.appendChild(toggleButton);
                    }

                    codeContainer.appendChild(supplierCountContainer);
                    codeCell.appendChild(codeContainer);
                } else {
                    // Add dummy spacing cell for non-first rows to maintain column alignment
                    codeCell.className = 'w-48 p-0 border-r'; // Match the width and border of the original cell
                    codeCell.style.backgroundColor = 'transparent';
                }
                row.appendChild(codeCell);

                // Brand (shared column - only shown in the first row of each group)
                const brandCell = UI.createTableCell({
                    className: isFirstRow ? 'font-medium bg-muted/50 border-r' : ''
                });
                if (isFirstRow) {
                    brandCell.rowSpan = 1; // Start with 1, will be updated by toggle function
                    brandCell.setAttribute('data-original-rowspan', products.length); // Store original rowspan

                    // Create a container for the brand information
                    const brandContainer = document.createElement('div');
                    brandContainer.className = 'flex flex-col';

                    if (mostFrequentBrand) {
                        // Try to load brand logo, fallback to text badge
                        const brandLogoPath = `/brands_logo/${mostFrequentBrand}.PNG`;

                        // Create container for brand display with larger fixed dimensions
                        const brandDisplay = document.createElement('div');
                        brandDisplay.className = 'w-40 h-16 flex items-center justify-start bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-3';

                        // Create image element for logo
                        const brandLogo = document.createElement('img');
                        brandLogo.src = brandLogoPath;
                        brandLogo.alt = mostFrequentBrand;
                        brandLogo.className = 'w-full h-full object-contain';
                        brandLogo.style.display = 'none'; // Initially hidden

                        // Create text fallback badge
                        const brandBadge = document.createElement('span');
                        brandBadge.className = 'px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-full text-xs font-medium inline-flex items-center w-fit';
                        brandBadge.innerHTML = `<i class="fas fa-tag mr-1"></i>${mostFrequentBrand}`;

                        // Handle logo loading
                        brandLogo.onload = function() {
                            // Logo loaded successfully, show it and hide text badge
                            brandLogo.style.display = 'block';
                            brandBadge.style.display = 'none';
                            // Update container styling for logo display
                            brandDisplay.className = 'w-40 h-16 flex items-center justify-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-3';
                        };

                        brandLogo.onerror = function() {
                            // Logo failed to load, keep text badge visible
                            brandLogo.style.display = 'none';
                            brandBadge.style.display = 'inline-flex';
                            // Update container styling for text badge display
                            brandDisplay.className = 'w-40 h-16 flex items-center justify-start bg-transparent rounded-lg p-2';
                        };

                        // Add both elements to display container
                        brandDisplay.appendChild(brandLogo);
                        brandDisplay.appendChild(brandBadge);
                        brandContainer.appendChild(brandDisplay);

                        // If there are multiple brands, add a note
                        const uniqueBrands = Object.keys(brandCounts).length;
                        if (uniqueBrands > 1) {
                            const brandNote = document.createElement('div');
                            brandNote.className = 'text-xs text-muted-foreground mt-2 italic';
                            brandNote.textContent = `+ ${uniqueBrands - 1} other brand${uniqueBrands > 2 ? 's' : ''}`;
                            brandContainer.appendChild(brandNote);
                        }
                    } else {
                        // No brand available
                        const noBrandText = document.createElement('span');
                        noBrandText.className = 'text-sm text-muted-foreground italic';
                        noBrandText.textContent = 'No brand info';
                        brandContainer.appendChild(noBrandText);
                    }

                    brandCell.appendChild(brandContainer);
                } else {
                    // Add dummy spacing cell for non-first rows to maintain column alignment
                    brandCell.className = 'w-40 p-0 border-r'; // Match the width and border of the original cell
                    brandCell.style.backgroundColor = 'transparent';
                }
                row.appendChild(brandCell);

                // Supplier
                const supplierCell = document.createElement('td');
                supplierCell.className = 'py-3 px-4 whitespace-nowrap';
                const supplierBadge = document.createElement('span');
                supplierBadge.className = 'px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full';
                supplierBadge.textContent = product.supplier || product.provider || '';
                supplierCell.appendChild(supplierBadge);
                row.appendChild(supplierCell);

                // Price (with exchange value chip below)
                const priceCell = document.createElement('td');
                priceCell.className = 'py-3 px-4 font-medium';
                const price = product.priceGross || product.retailPrice || product.price || '';

                // Check if this is the first (lowest price) offer in the sorted list
                const isLowestPrice = index === 0;

                // Create a container for price and exchange value
                const priceContainer = document.createElement('div');
                priceContainer.className = 'flex flex-col space-y-1';

                if (price) {
                    const priceSpan = document.createElement('span');
                    // Highlight the lowest price offer with a different style
                    priceSpan.className = isLowestPrice
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded font-bold flex items-center'
                        : 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-2 py-1 rounded';

                    // Add a "Lowest Price" indicator for the first item
                    if (isLowestPrice && sortedProducts.length > 1) {
                        const lowestPriceIcon = document.createElement('i');
                        lowestPriceIcon.className = 'fas fa-tags text-yellow-500 mr-1';
                        priceSpan.appendChild(lowestPriceIcon);

                        const priceText = document.createTextNode(price);
                        priceSpan.appendChild(priceText);
                    } else {
                        priceSpan.textContent = price;
                    }

                    priceContainer.appendChild(priceSpan);

                    // Add exchange value chip below the price if it exists
                    if (product.exchangeValue) {
                        // Parse exchange value for calculations
                        let exchangeValue = 0;
                        if (typeof product.exchangeValue === 'string') {
                            exchangeValue = parseFloat(product.exchangeValue.replace(/[^\d.,]/g, '').replace(',', '.')) || 0;
                            exchangeValue = Math.abs(exchangeValue);
                        } else {
                            exchangeValue = Math.abs(parseFloat(product.exchangeValue) || 0);
                        }

                        const exchangeChip = document.createElement('span');
                        exchangeChip.className = 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400 px-2 py-1 rounded text-xs font-medium inline-flex items-center w-fit';
                        exchangeChip.innerHTML = `<i class="fas fa-exchange-alt mr-1"></i>${product.exchangeValue}`;
                        priceContainer.appendChild(exchangeChip);
                    }

                    priceCell.appendChild(priceContainer);
                } else {
                    priceCell.textContent = '-';
                }
                row.appendChild(priceCell);

                // Availability (using availabilitySummary with color-coded chips)
                const availabilityCell = document.createElement('td');
                availabilityCell.className = 'py-3 px-4';

                if (product.availabilitySummary) {
                    const availSpan = document.createElement('span');
                    const summary = product.availabilitySummary;

                    // Color-coded chips based on availability summary
                    if (summary === 'Available') {
                        availSpan.className = 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-2 py-1 rounded text-sm font-medium inline-flex items-center';
                        availSpan.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Available';
                    } else if (summary === 'External Delivery Only') {
                        availSpan.className = 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 px-2 py-1 rounded text-sm font-medium inline-flex items-center';
                        availSpan.innerHTML = '<i class="fas fa-truck mr-1"></i>External Delivery';
                    } else if (summary === 'Unavailable') {
                        availSpan.className = 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 px-2 py-1 rounded text-sm font-medium inline-flex items-center';
                        availSpan.innerHTML = '<i class="fas fa-times-circle mr-1"></i>Unavailable';
                    } else {
                        // Fallback for any other values
                        availSpan.className = 'bg-muted text-muted-foreground px-2 py-1 rounded text-sm font-medium';
                        availSpan.textContent = summary;
                    }

                    availabilityCell.appendChild(availSpan);
                } else {
                    // Fallback to raw availability if availabilitySummary is not available
                    if (product.availability) {
                        const availSpan = document.createElement('span');
                        availSpan.className = 'bg-muted text-muted-foreground px-2 py-1 rounded text-sm';
                        availSpan.textContent = product.availability;
                        availabilityCell.appendChild(availSpan);
                    } else {
                        availabilityCell.textContent = '-';
                    }
                }
                row.appendChild(availabilityCell);

                // Delivery
                const deliveryCell = document.createElement('td');
                deliveryCell.className = 'py-3 px-4 text-sm text-muted-foreground';
                deliveryCell.textContent = product.delivery || product.delivery_options || '-';
                row.appendChild(deliveryCell);

                // Status (using returnable field with color-coded chips)
                const statusCell = document.createElement('td');
                statusCell.className = 'py-3 px-4';

                if (product.returnable !== null && product.returnable !== undefined) {
                    const statusSpan = document.createElement('span');

                    // Convert returnable value to display text and styling
                    if (product.returnable === true || product.returnable === 'true' || product.returnable === 1 || product.returnable === '1') {
                        statusSpan.className = 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-2 py-1 rounded text-sm font-medium inline-flex items-center status-chip';
                        statusSpan.innerHTML = '<i class="fas fa-undo mr-1"></i>Returnable';
                    } else if (product.returnable === false || product.returnable === 'false' || product.returnable === 0 || product.returnable === '0') {
                        statusSpan.className = 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 px-2 py-1 rounded text-sm font-medium inline-flex items-center status-chip';
                        statusSpan.innerHTML = '<i class="fas fa-ban mr-1"></i>Non-returnable';
                    } else {
                        // Fallback for any other values
                        statusSpan.className = 'bg-muted text-muted-foreground px-2 py-1 rounded text-sm font-medium status-chip';
                        statusSpan.textContent = product.returnable;
                    }

                    statusCell.appendChild(statusSpan);
                } else {
                    // Show dash for null/undefined values
                    statusCell.textContent = '-';
                }
                row.appendChild(statusCell);

                resultsBody.appendChild(row);

                // Mark that we've processed the first row for this group
                isFirstRow = false;
            });

            // Add a distinct separator between groups if not the last group
            if (productCode !== productCodes[productCodes.length - 1]) {
                const separatorRow = document.createElement('tr');
                separatorRow.className = 'h-4 bg-muted/30';

                const separatorCell = document.createElement('td');
                separatorCell.colSpan = 7; // Fixed 7 columns: Product Code, Brand, Supplier, Price, Status, Availability, Delivery
                separatorCell.className = 'separator-cell p-0 border-t border-b border-border';

                separatorRow.appendChild(separatorCell);
                resultsBody.appendChild(separatorRow);
            }
        });
    }

    // Show error message
    function showError(message) {
        errorMessage.textContent = message;
        errorContainer.classList.remove('hidden');
        resultsContainer.classList.add('hidden');
    }

    // Toggle product group collapse/expand
    function toggleProductGroup(productCode, buttonElement) {
        const sanitizedCode = productCode.replace(/[^a-zA-Z0-9]/g, '_');
        const allRows = document.querySelectorAll(`.product-row-${sanitizedCode}`);
        const collapsedRows = document.querySelectorAll(`.product-row-${sanitizedCode}.collapsed-row`);

        const toggleText = buttonElement.querySelector('.toggle-text');
        const toggleIcon = buttonElement.querySelector('.toggle-icon');

        // Find the first row (contains the shared cells)
        const firstRow = document.querySelector(`.product-row-${sanitizedCode}[data-row-index="0"]`);
        const codeCell = firstRow ? firstRow.querySelector('td[data-original-rowspan]') : null;
        const brandCell = firstRow ? firstRow.querySelectorAll('td[data-original-rowspan]')[1] : null;

        const isCurrentlyCollapsed = collapsedRows.length > 0 && collapsedRows[0].style.display === 'none';

        if (isCurrentlyCollapsed) {
            // Expand: Show all rows
            collapsedRows.forEach(row => {
                row.style.display = '';
                row.classList.add('animate-fade-in');
            });

            // Update rowSpan to span all rows
            if (codeCell) {
                const originalRowSpan = parseInt(codeCell.getAttribute('data-original-rowspan'));
                codeCell.rowSpan = originalRowSpan;
            }
            if (brandCell) {
                const originalRowSpan = parseInt(brandCell.getAttribute('data-original-rowspan'));
                brandCell.rowSpan = originalRowSpan;
            }

            toggleText.textContent = 'Show Less';
            toggleIcon.style.transform = 'rotate(180deg)';
            buttonElement.setAttribute('data-expanded', 'true');
        } else {
            // Collapse: Hide all rows except the first one
            collapsedRows.forEach(row => {
                row.style.display = 'none';
                row.classList.remove('animate-fade-in');
            });

            // Update rowSpan to span only the first row
            if (codeCell) {
                codeCell.rowSpan = 1;
            }
            if (brandCell) {
                brandCell.rowSpan = 1;
            }

            toggleText.textContent = 'Show All';
            toggleIcon.style.transform = 'rotate(0deg)';
            buttonElement.setAttribute('data-expanded', 'false');
        }
    }

    // Reset UI
    function resetUI() {
        errorContainer.classList.add('hidden');
        resultsContainer.classList.add('hidden');
        progressLog.textContent = '';
        scrapersStatus.innerHTML = '';

        // Reset error tracking
        scraperErrors = {};
        errorLogsFab.classList.add('hidden');
        errorCount.textContent = '0';

        // Hide error logs modal if it's open
        errorLogsModal.classList.add('hidden');

        // Hide welcome hero when starting a search
        welcomeHero.classList.add('hidden');

        // Reset filter inputs to their default state
        filterInput.value = '';

        // Reset availability filter checkboxes to checked (default state)
        availabilityAvailableFilter.checked = true;
        availabilityExternalFilter.checked = true;
        availabilityUnavailableFilter.checked = true;

        // Reset price range slider to full range (show all)
        priceSliderMin.value = priceSliderMin.min;
        priceSliderMax.value = priceSliderMax.max;
        priceMinValue.textContent = `${priceSliderMin.min} RON`;
        priceMaxValue.textContent = `${priceSliderMax.max} RON`;
        updateRangeFill();
    }

    // Show welcome hero (when no search is active)
    function showWelcomeHero() {
        welcomeHero.classList.remove('hidden');
        loadingElement.classList.add('hidden');
        resultsContainer.classList.add('hidden');
        errorContainer.classList.add('hidden');
    }

    // Update error logs FAB
    function updateErrorLogsFab() {
        const errorCountValue = Object.keys(scraperErrors).length;

        if (errorCountValue > 0) {
            // Update error count badge
            errorCount.textContent = errorCountValue;

            // Show the FAB
            errorLogsFab.classList.remove('hidden');
        } else {
            // Hide the FAB
            errorLogsFab.classList.add('hidden');
        }
    }

    // Show error logs modal
    function showErrorLogsModal() {
        // Clear previous content
        errorLogsContainer.innerHTML = '';

        // Add error logs for each failed scraper
        Object.entries(scraperErrors).forEach(([scraper, errorData]) => {
            const errorCard = document.createElement('div');
            errorCard.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-6';

            // Create header with scraper name
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-3';

            const titleContainer = document.createElement('div');
            titleContainer.className = 'flex items-center';

            const icon = document.createElement('i');
            icon.className = 'fas fa-robot text-red-500 mr-2';

            const title = document.createElement('h4');
            title.className = 'font-bold text-red-700 text-lg';
            title.textContent = scraper;

            titleContainer.appendChild(icon);
            titleContainer.appendChild(title);

            header.appendChild(titleContainer);

            errorCard.appendChild(header);

            // Extract error message and logs
            const errorMessage = typeof errorData === 'object' ? errorData.message : errorData;
            const scraperLogs = typeof errorData === 'object' ? errorData.logs : '';

            // Create error message section
            const messageSection = document.createElement('div');
            messageSection.className = 'mb-4';

            const messageHeader = document.createElement('h5');
            messageHeader.className = 'text-red-700 font-medium mb-2 flex items-center';
            messageHeader.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i> Error Message';

            const message = document.createElement('div');
            message.className = 'text-red-600 text-sm font-mono bg-red-100 p-3 rounded overflow-x-auto whitespace-pre-wrap max-h-40 overflow-y-auto';

            // Format the error message for better readability
            let formattedMessage = errorMessage;

            // Check if it's a stack trace (contains multiple lines with "at")
            if (errorMessage && errorMessage.includes('\n') && errorMessage.includes('at ')) {
                // It's likely a stack trace, format it nicely
                const lines = errorMessage.split('\n');
                formattedMessage = lines.map(line => {
                    // Highlight the main error message
                    if (!line.trim().startsWith('at ')) {
                        return `<span class="font-bold text-red-700">${line}</span>`;
                    }
                    return line;
                }).join('\n');

                message.innerHTML = formattedMessage;
            } else {
                message.textContent = errorMessage || 'Unknown error';
            }

            messageSection.appendChild(messageHeader);
            messageSection.appendChild(message);

            errorCard.appendChild(messageSection);

            // Create scraper logs section if logs exist
            if (scraperLogs && scraperLogs.trim()) {
                const logsSection = document.createElement('div');
                logsSection.className = 'mb-4';

                const logsHeader = document.createElement('h5');
                logsHeader.className = 'text-gray-700 font-medium mb-2 flex items-center';
                logsHeader.innerHTML = '<i class="fas fa-terminal mr-2"></i> Scraper Logs';

                // Format the logs for better readability
                const formattedLogs = formatScraperLogs(scraperLogs, scraper);

                const logs = document.createElement('pre');
                logs.className = 'text-gray-600 text-xs font-mono bg-gray-100 p-3 rounded overflow-x-auto max-h-60 overflow-y-auto';
                logs.innerHTML = formattedLogs;

                logsSection.appendChild(logsHeader);
                logsSection.appendChild(logs);

                errorCard.appendChild(logsSection);
            }

            // Add a note about how to fix common issues
            const helpNote = document.createElement('div');
            helpNote.className = 'mt-3 text-xs text-gray-600 bg-gray-50 p-2 rounded border border-gray-200';
            helpNote.innerHTML = '<strong>Note for testers:</strong> This error may be caused by network issues, authentication problems, or changes to the supplier website structure. Please report this error to the development team.';

            errorCard.appendChild(helpNote);

            errorLogsContainer.appendChild(errorCard);
        });

        // Show the modal
        errorLogsModal.classList.remove('hidden');
    }

    // Format scraper logs for better readability
    function formatScraperLogs(logs, scraperName) {
        if (!logs) return '';

        // Split logs into lines
        const lines = logs.split('\n');

        // Process each line
        const formattedLines = lines.map(line => {
            // Highlight error messages
            if (line.includes('Error') || line.includes('error') || line.includes('❌') || line.includes('failed') || line.includes('Failed')) {
                return `<span class="text-red-600 font-semibold">${escapeHtml(line)}</span>`;
            }

            // Highlight success messages
            if (line.includes('✅') || line.includes('success') || line.includes('Success') || line.includes('completed')) {
                return `<span class="text-green-600">${escapeHtml(line)}</span>`;
            }

            // Highlight scraper name
            if (line.includes(scraperName)) {
                return line.replace(new RegExp(scraperName, 'g'), `<span class="font-semibold">${scraperName}</span>`);
            }

            // Return the line as is
            return escapeHtml(line);
        });

        return formattedLines.join('\n');
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Event listeners for error logs modal
    showErrorLogsBtn.addEventListener('click', showErrorLogsModal);

    closeErrorLogsModal.addEventListener('click', () => {
        errorLogsModal.classList.add('hidden');
    });

    closeErrorLogsBtn.addEventListener('click', () => {
        errorLogsModal.classList.add('hidden');
    });

    // Close modal when clicking outside
    errorLogsModal.addEventListener('click', (e) => {
        if (e.target === errorLogsModal) {
            errorLogsModal.classList.add('hidden');
        }
    });

    // Dark Mode Functionality
    function initTheme() {
        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add('dark');
            themeIcon.className = 'fas fa-sun w-4 h-4';
            headerLogo.src = '/logo_dark_mode.png';
            footerLogo.src = '/logo_dark_mode.png';
        } else {
            document.documentElement.classList.remove('dark');
            themeIcon.className = 'fas fa-moon w-4 h-4';
            headerLogo.src = '/logo_light_mode.png';
            footerLogo.src = '/logo_light_mode.png';
        }
    }

    function toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');

        if (isDark) {
            // Switch to light mode
            document.documentElement.classList.remove('dark');
            themeIcon.className = 'fas fa-moon w-4 h-4';
            headerLogo.src = '/logo_light_mode.png';
            footerLogo.src = '/logo_light_mode.png';
            localStorage.setItem('theme', 'light');
        } else {
            // Switch to dark mode
            document.documentElement.classList.add('dark');
            themeIcon.className = 'fas fa-sun w-4 h-4';
            headerLogo.src = '/logo_dark_mode.png';
            footerLogo.src = '/logo_dark_mode.png';
            localStorage.setItem('theme', 'dark');
        }
    }

    // Initialize theme on page load
    initTheme();

    // Add event listener for theme toggle
    themeToggle.addEventListener('click', toggleTheme);

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            if (e.matches) {
                document.documentElement.classList.add('dark');
                themeIcon.className = 'fas fa-sun w-4 h-4';
                headerLogo.src = '/logo_dark_mode.png';
                footerLogo.src = '/logo_dark_mode.png';
            } else {
                document.documentElement.classList.remove('dark');
                themeIcon.className = 'fas fa-moon w-4 h-4';
                headerLogo.src = '/logo_light_mode.png';
                footerLogo.src = '/logo_light_mode.png';
            }
        }
    });
});
