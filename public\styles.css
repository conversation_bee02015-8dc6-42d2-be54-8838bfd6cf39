/* shadcn/ui CSS Variables and Base Styles */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Improve text rendering */
  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  /* Enhanced focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

/* Custom utility classes for shadcn/ui */
.bg-background { background-color: hsl(var(--background)); }
.bg-foreground { background-color: hsl(var(--foreground)); }
.bg-card { background-color: hsl(var(--card)); }
.bg-card-foreground { background-color: hsl(var(--card-foreground)); }
.bg-popover { background-color: hsl(var(--popover)); }
.bg-popover-foreground { background-color: hsl(var(--popover-foreground)); }
.bg-primary { background-color: hsl(var(--primary)); }
.bg-primary-foreground { background-color: hsl(var(--primary-foreground)); }
.bg-secondary { background-color: hsl(var(--secondary)); }
.bg-secondary-foreground { background-color: hsl(var(--secondary-foreground)); }
.bg-muted { background-color: hsl(var(--muted)); }
.bg-muted-foreground { background-color: hsl(var(--muted-foreground)); }
.bg-accent { background-color: hsl(var(--accent)); }
.bg-accent-foreground { background-color: hsl(var(--accent-foreground)); }
.bg-destructive { background-color: hsl(var(--destructive)); }
.bg-destructive-foreground { background-color: hsl(var(--destructive-foreground)); }

.text-background { color: hsl(var(--background)); }
.text-foreground { color: hsl(var(--foreground)); }
.text-card { color: hsl(var(--card)); }
.text-card-foreground { color: hsl(var(--card-foreground)); }
.text-popover { color: hsl(var(--popover)); }
.text-popover-foreground { color: hsl(var(--popover-foreground)); }
.text-primary { color: hsl(var(--primary)); }
.text-primary-foreground { color: hsl(var(--primary-foreground)); }
.text-secondary { color: hsl(var(--secondary)); }
.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
.text-muted { color: hsl(var(--muted)); }
.text-muted-foreground { color: hsl(var(--muted-foreground)); }
.text-accent { color: hsl(var(--accent)); }
.text-accent-foreground { color: hsl(var(--accent-foreground)); }
.text-destructive { color: hsl(var(--destructive)); }
.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.border-border { border-color: hsl(var(--border)); }
.border-input { border-color: hsl(var(--input)); }
.ring-ring { --tw-ring-color: hsl(var(--ring)); }
.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }

/* Custom animations and transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Status indicator animations */
@keyframes pulse-success {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes pulse-error {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.pulse-success {
  animation: pulse-success 2s infinite;
}

.pulse-error {
  animation: pulse-error 1.5s infinite;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Enhanced shadcn/ui specific styles */
/* Popular shadcn/ui header patterns:
   Option 1 (Current): bg-muted/30 - Subtle muted background (most popular)
   Option 2: bg-background/95 - Clean transparent background
   Option 3: bg-card - Solid card background
   Option 4: bg-background border-b - Minimal border-only approach
*/
.shadcn-header {
  background: hsl(var(--muted) / 0.3);
  border-bottom: 1px solid hsl(var(--border));
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.shadcn-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.shadcn-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Improved button focus states */
.shadcn-button:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Better table styling */
.shadcn-table {
  border-collapse: separate;
  border-spacing: 0;
}

.shadcn-table th {
  background: hsl(var(--muted) / 0.5);
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
}

.shadcn-table td {
  border-bottom: 1px solid hsl(var(--border));
}

.shadcn-table tr:hover td {
  background: hsl(var(--muted) / 0.5);
}

/* Legacy support for existing classes */
.hidden {
  display: none;
}

/* Enhanced table layout for wider content area */
.results-table-container {
  max-width: 100%;
  overflow-x: auto;
}

/* Ensure the main container can accommodate wider tables */
.container {
  max-width: 1600px;
}

/* Better table responsiveness */
@media (max-width: 1600px) {
  .container {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Improved status column styling */
.status-chip {
  white-space: nowrap;
  min-width: fit-content;
}

/* Better table cell spacing for wider layout */
.shadcn-table td {
  padding: 0.75rem 0.75rem;
}

.shadcn-table th {
  padding: 0.75rem 0.75rem;
}

/* Optimize table layout for better space utilization */
.shadcn-table {
  table-layout: fixed;
  width: 100%;
}

/* Allow text wrapping in table cells for better content display */
.shadcn-table td {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure table container has proper scrolling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
}

.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Availability filter styling */
.availability-filter {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 1rem;
}

.availability-filter h4 {
  margin-bottom: 0.75rem;
  font-weight: 500;
}

/* Custom checkbox styling for better visual consistency */
input[type="checkbox"] {
  accent-color: hsl(var(--primary));
}

/* Availability status indicators */
.availability-indicator {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

/* Pulse animation for FAB */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Hero section animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

.hero-float {
  animation: float 3s ease-in-out infinite;
}

.hero-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Staggered animation delays for floating elements */
.animate-bounce-1 { animation-delay: 0.5s; }
.animate-bounce-2 { animation-delay: 1s; }
.animate-bounce-3 { animation-delay: 1.5s; }
.animate-bounce-4 { animation-delay: 2s; }

/* Feature card hover effects */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Double-sided price range slider styling */
.price-range-container {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
}

.price-range-slider {
  position: absolute;
  width: 100%;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  pointer-events: none;
}

.price-range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: hsl(var(--primary));
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  pointer-events: all;
  position: relative;
  z-index: 2;
}

.price-range-slider::-webkit-slider-thumb:hover {
  background: hsl(var(--primary) / 0.8);
  transform: scale(1.1);
}

.price-range-slider::-moz-range-thumb {
  background: hsl(var(--primary));
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  pointer-events: all;
  position: relative;
  z-index: 2;
}

.price-range-slider::-moz-range-thumb:hover {
  background: hsl(var(--primary) / 0.8);
  transform: scale(1.1);
}

.price-range-slider::-webkit-slider-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
}

.price-range-slider::-moz-range-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.price-range-track {
  position: absolute;
  width: 100%;
  height: 8px;
  background: hsl(var(--muted));
  border-radius: 4px;
  z-index: 1;
}

.price-range-fill {
  position: absolute;
  height: 8px;
  background: hsl(var(--primary) / 0.3);
  border-radius: 4px;
  z-index: 1;
  transition: all 0.2s ease;
}

/* Differentiate the two slider handles */
.price-range-slider-min::-webkit-slider-thumb {
  background: hsl(var(--primary));
}

.price-range-slider-max::-webkit-slider-thumb {
  background: hsl(var(--primary));
  border: 2px solid white;
}

.price-range-slider-min::-moz-range-thumb {
  background: hsl(var(--primary));
}

.price-range-slider-max::-moz-range-thumb {
  background: hsl(var(--primary));
  border: 2px solid white;
}
